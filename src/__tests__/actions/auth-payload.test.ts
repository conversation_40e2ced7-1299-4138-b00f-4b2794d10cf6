/**
 * Test to verify the correct payload structure for authentication Server Actions
 */

import { splitBrazilianPhoneNumber } from '@/src/app/_utils/validation/phoneValidation';

describe('Authentication Payload Structure', () => {
  describe('splitBrazilianPhoneNumber', () => {
    it('should correctly split a Brazilian phone number into phoneCode and phoneNumber', () => {
      const phoneNumber = '11949073954';
      const result = splitBrazilianPhoneNumber(phoneNumber);

      expect(result).toEqual({
        phoneCode: '11',
        phoneNumber: '949073954',
        fullNumber: '(11) 94907-3954',
      });
    });

    it('should handle formatted phone numbers', () => {
      const phoneNumber = '(11) 94907-3954';
      const result = splitBrazilianPhoneNumber(phoneNumber);

      expect(result).toEqual({
        phoneCode: '11',
        phoneNumber: '949073954',
        fullNumber: '(11) 94907-3954',
      });
    });

    it('should handle phone numbers with spaces and special characters', () => {
      const phoneNumber = '(11) 9 4907-3954';
      const result = splitBrazilianPhoneNumber(phoneNumber);

      expect(result).toEqual({
        phoneCode: '11',
        phoneNumber: '949073954',
        fullNumber: '(11) 94907-3954',
      });
    });

    it('should throw error for invalid phone number length', () => {
      expect(() => splitBrazilianPhoneNumber('1194907395')).toThrow(
        'Invalid Brazilian phone number: must have 11 digits'
      );
    });

    it('should throw error for invalid DDD', () => {
      expect(() => splitBrazilianPhoneNumber('00949073954')).toThrow('Invalid Brazilian DDD: 00');
    });
  });

  describe('API Payload Structure', () => {
    it('should create correct payload structure for phone verification', () => {
      const phoneNumber = '11949073954';
      const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumber);

      const expectedPayload = {
        phoneCode: '11',
        phoneNumber: '949073954',
      };

      const actualPayload = {
        phoneCode: phoneNumberParts.phoneCode,
        phoneNumber: phoneNumberParts.phoneNumber,
      };

      expect(actualPayload).toEqual(expectedPayload);
      expect(JSON.stringify(actualPayload)).toBe(JSON.stringify(expectedPayload));
    });

    it('should create correct payload structure for OTP verification', () => {
      const phoneNumber = '11949073954';
      const otpCode = '123456';
      const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumber);

      const expectedPayload = {
        phoneCode: '11',
        phoneNumber: '949073954',
        token: '123456',
      };

      const actualPayload = {
        phoneCode: phoneNumberParts.phoneCode,
        phoneNumber: phoneNumberParts.phoneNumber,
        token: otpCode,
      };

      expect(actualPayload).toEqual(expectedPayload);
      expect(JSON.stringify(actualPayload)).toBe(JSON.stringify(expectedPayload));
    });

    it('should NOT create an array payload', () => {
      const phoneNumber = '11949073954';
      const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumber);

      const correctPayload = {
        phoneCode: phoneNumberParts.phoneCode,
        phoneNumber: phoneNumberParts.phoneNumber,
      };

      // Verify it's an object, not an array
      expect(Array.isArray(correctPayload)).toBe(false);
      expect(typeof correctPayload).toBe('object');
      expect(correctPayload).toHaveProperty('phoneCode');
      expect(correctPayload).toHaveProperty('phoneNumber');

      // Verify the JSON string is an object, not an array
      const jsonString = JSON.stringify(correctPayload);
      expect(jsonString.startsWith('{')).toBe(true);
      expect(jsonString.endsWith('}')).toBe(true);
      expect(jsonString.startsWith('[')).toBe(false);
    });
  });
});
