/**
 * Test to verify OTP validation logic is working correctly
 */

import { verifyOtpCode } from '@/src/app/_actions/auth';

// Mock the fetch function
global.fetch = jest.fn();

// Mock the cookies function
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    set: jest.fn(),
    delete: jest.fn(),
  })),
}));

// Mock the phone validation utility
jest.mock('@/src/app/_utils/validation/phoneValidation', () => ({
  splitBrazilianPhoneNumber: jest.fn((phoneNumber: string) => ({
    phoneCode: '11',
    phoneNumber: '949073954',
    fullNumber: '(11) 94907-3954',
  })),
}));

describe('OTP Validation Logic', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.error to avoid noise in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('verifyOtpCode', () => {
    it('should return success: false when API returns success: false', async () => {
      // Mock API response with success: false
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: false,
          error: 'Invalid OTP code',
        }),
      });

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        type: 'INVALID_OTP',
        message: 'Invalid OTP code',
        details: 'API returned success: false',
      });
    });

    it('should return success: true when API returns success: true with valid data', async () => {
      // Mock API response with success: true and valid data
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          token: 'valid-jwt-token',
          userId: 'user-123',
        }),
      });

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        success: true,
        token: 'valid-jwt-token',
        userId: 'user-123',
      });
    });

    it('should return success: false when API returns success: true but missing required fields', async () => {
      // Mock API response with success: true but missing token
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          userId: 'user-123',
          // token is missing
        }),
      });

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        type: 'SERVER_ERROR',
        message: 'Resposta inválida do servidor. Tente novamente.',
        details: 'Missing token or userId in successful response',
      });
    });

    it('should return success: false when HTTP response is not ok (400)', async () => {
      // Mock HTTP 400 response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        text: async () => 'Bad Request',
      });

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        type: 'INVALID_OTP',
        message: 'Código de verificação inválido ou expirado',
        details: 'Bad Request',
      });
    });

    it('should return success: false when HTTP response is not ok (404)', async () => {
      // Mock HTTP 404 response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        text: async () => 'Not Found',
      });

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        type: 'SERVER_ERROR',
        message: 'Sessão de verificação não encontrada',
        details: 'Not Found',
      });
    });

    it('should return success: false when HTTP response is not ok (500)', async () => {
      // Mock HTTP 500 response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => 'Internal Server Error',
      });

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        type: 'SERVER_ERROR',
        message: 'Erro interno do servidor. Tente novamente.',
        details: 'Internal Server Error',
      });
    });

    it('should return success: false when network error occurs', async () => {
      // Mock network error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await verifyOtpCode('11949073954', '123456');

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        type: 'NETWORK_ERROR',
        message: 'Erro de conexão. Verifique sua internet e tente novamente.',
        details: 'Network error',
      });
    });

    it('should send correct request payload to API', async () => {
      // Mock successful API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          token: 'valid-jwt-token',
          userId: 'user-123',
        }),
      });

      await verifyOtpCode('11949073954', '123456');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/authenticator/confirm'),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'service-provider': 'EUR',
          },
          body: JSON.stringify({
            phoneCode: '11',
            phoneNumber: '949073954',
            token: '123456',
          }),
        }
      );
    });
  });
});
