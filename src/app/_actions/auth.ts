'use server';

import {
  AUTH_COOKIES,
  AUTH_COOKIE_CONFIG,
  AUTH_ERROR_MESSAGES,
  AUTH_HTTP_STATUS,
} from '@/src/app/_constants/auth';
import type {
  AuthError,
  OtpConfirmationRequest,
  OtpConfirmationResponse,
  OtpConfirmationResult,
  PhoneVerificationRequest,
  PhoneVerificationResponse,
  PhoneVerificationResult,
} from '@/src/app/_interfaces';
import { phoneVerificationFormDataSchema } from '@/src/app/_utils/validation/authValidation';
import { splitBrazilianPhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import { cookies } from 'next/headers';

const API_BASE_URL = process.env.NEXT_PRIVATE_API_BASE_URL;

if (!API_BASE_URL) {
  throw new Error('NEXT_PRIVATE_API_BASE_URL environment variable is not set');
}

/**
 * HTTP client for authentication API calls
 */
async function authApiRequest<T>(
  endpoint: string,
  options: {
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: unknown;
  }
): Promise<{ data: T; response: Response }> {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    method: options.method,
    headers: {
      'Content-Type': 'application/json',
      'service-provider': 'EUR',
    },
    body: options.body ? JSON.stringify(options.body) : undefined,
  });

  const data = await response.json();
  return { data, response };
}

/**
 * Creates an AuthError object with proper typing
 */
function createAuthError(type: AuthError['type'], message: string, details?: string): AuthError {
  return { type, message, details };
}

/**
 * Normalizes phone number parameter handling for Next.js 15 serialization issues
 */
function normalizePhoneNumber(phoneNumber: string | string[]): string {
  if (Array.isArray(phoneNumber)) {
    return String(phoneNumber[0]).trim();
  }
  return String(phoneNumber).trim();
}

/**
 * Maps HTTP status codes to appropriate error types and messages
 */
function mapHttpErrorToAuthError(status: number, errorText: string): AuthError {
  switch (status) {
    case AUTH_HTTP_STATUS.BAD_REQUEST:
      return createAuthError('INVALID_PHONE', AUTH_ERROR_MESSAGES.INVALID_PHONE, errorText);
    case AUTH_HTTP_STATUS.NOT_FOUND:
      return createAuthError('SERVER_ERROR', AUTH_ERROR_MESSAGES.PHONE_NOT_FOUND, errorText);
    case AUTH_HTTP_STATUS.INTERNAL_SERVER_ERROR:
    case AUTH_HTTP_STATUS.BAD_GATEWAY:
    case AUTH_HTTP_STATUS.SERVICE_UNAVAILABLE:
    case AUTH_HTTP_STATUS.GATEWAY_TIMEOUT:
      return createAuthError('SERVER_ERROR', AUTH_ERROR_MESSAGES.SERVER_ERROR, errorText);
    default:
      return createAuthError('SERVER_ERROR', AUTH_ERROR_MESSAGES.SEND_CODE_ERROR, errorText);
  }
}

/**
 * Server Action: Send phone verification code (FormData version)
 * Calls POST /api/v1/authenticator to validate phone and send OTP
 */
export async function sendPhoneVerificationAction(
  formData: FormData
): Promise<PhoneVerificationResult> {
  try {
    const phoneNumber = formData.get('phoneNumber') as string;

    // Validate input using Zod schema
    const validationResult = phoneVerificationFormDataSchema.safeParse({ phoneNumber });

    if (!validationResult.success) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          validationResult.error.errors[0]?.message || AUTH_ERROR_MESSAGES.INVALID_PHONE,
          'Form validation failed'
        ),
      };
    }

    return sendPhoneVerification(validationResult.data.phoneNumber);
  } catch (error) {
    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        AUTH_ERROR_MESSAGES.NETWORK_ERROR,
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Send phone verification code (Wrapper for client calls)
 * This version handles the Next.js 15 parameter serialization issue
 */
export async function sendPhoneVerificationWrapper(
  phoneNumber: string | string[]
): Promise<PhoneVerificationResult> {
  'use server';
  return sendPhoneVerification(normalizePhoneNumber(phoneNumber));
}

/**
 * Server Action: Send phone verification code (Direct call version)
 * This version ensures proper parameter handling for direct calls
 */
export async function sendPhoneVerificationDirect(
  phoneNumber: string
): Promise<PhoneVerificationResult> {
  'use server';
  return sendPhoneVerification(normalizePhoneNumber(phoneNumber));
}

/**
 * Server Action: Send phone verification code
 * Calls POST /api/v1/authenticator to validate phone and send OTP
 */
export async function sendPhoneVerification(phoneNumber: string): Promise<PhoneVerificationResult> {
  try {
    // Normalize phone number parameter
    const normalizedPhoneNumber = normalizePhoneNumber(phoneNumber);

    // Validate and split the phone number
    const phoneNumberParts = splitBrazilianPhoneNumber(normalizedPhoneNumber);

    const requestPayload: PhoneVerificationRequest = {
      phoneCode: phoneNumberParts.phoneCode,
      phoneNumber: phoneNumberParts.phoneNumber,
    };

    const { data, response } = await authApiRequest<PhoneVerificationResponse>('/authenticator', {
      method: 'POST',
      body: requestPayload,
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: mapHttpErrorToAuthError(response.status, errorText),
      };
    }

    // Check if the API response indicates success
    // The API returns { "message": "Created Successfully", "status": 201 } for success
    // If there's a success field, use it; otherwise, assume success if we got here (response.ok was true)
    const isSuccess = data.success !== undefined ? data.success : true;

    if (!isSuccess) {
      return {
        success: false,
        error: createAuthError(
          'SERVER_ERROR',
          data.error || AUTH_ERROR_MESSAGES.VERIFICATION_FAILED,
          'API returned success: false'
        ),
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    if (error instanceof Error && error.message.includes('Invalid Brazilian')) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          AUTH_ERROR_MESSAGES.PHONE_VALIDATION_ERROR,
          error.message
        ),
      };
    }

    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        AUTH_ERROR_MESSAGES.NETWORK_ERROR,
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Maps HTTP status codes to appropriate OTP error types and messages
 */
function mapOtpHttpErrorToAuthError(status: number, errorText: string): AuthError {
  switch (status) {
    case AUTH_HTTP_STATUS.BAD_REQUEST:
      return createAuthError('INVALID_OTP', AUTH_ERROR_MESSAGES.INVALID_OTP, errorText);
    case AUTH_HTTP_STATUS.NOT_FOUND:
      return createAuthError('SERVER_ERROR', AUTH_ERROR_MESSAGES.SESSION_NOT_FOUND, errorText);
    case AUTH_HTTP_STATUS.INTERNAL_SERVER_ERROR:
    case AUTH_HTTP_STATUS.BAD_GATEWAY:
    case AUTH_HTTP_STATUS.SERVICE_UNAVAILABLE:
    case AUTH_HTTP_STATUS.GATEWAY_TIMEOUT:
      return createAuthError('SERVER_ERROR', AUTH_ERROR_MESSAGES.SERVER_ERROR, errorText);
    default:
      return createAuthError('SERVER_ERROR', AUTH_ERROR_MESSAGES.OTP_INVALID, errorText);
  }
}

/**
 * Sets authentication cookies with consistent configuration
 */
async function setAuthCookies(
  phoneNumberParts: { fullNumber: string },
  authData?: { token?: string; userId?: string }
): Promise<void> {
  const cookieStore = await cookies();

  if (authData?.token && authData?.userId) {
    // Set secure HTTP-only cookies for full authentication
    cookieStore.set(AUTH_COOKIES.TOKEN, authData.token, AUTH_COOKIE_CONFIG);
    cookieStore.set(AUTH_COOKIES.USER_ID, authData.userId, AUTH_COOKIE_CONFIG);
  }

  // Always set phone number and verification status
  cookieStore.set(AUTH_COOKIES.PHONE_NUMBER, phoneNumberParts.fullNumber, AUTH_COOKIE_CONFIG);
  cookieStore.set(AUTH_COOKIES.PHONE_VERIFIED, 'true', AUTH_COOKIE_CONFIG);
}

/**
 * Server Action: Verify OTP code (FormData version)
 * Calls POST /api/v1/authenticator/confirm to validate OTP and complete authentication
 */
export async function verifyOtpCodeAction(formData: FormData): Promise<OtpConfirmationResult> {
  try {
    const phoneNumber = formData.get('phoneNumber') as string;
    const otpCode = formData.get('otpCode') as string;

    // Validate input using Zod schema
    const validationResult = otpConfirmationFormDataSchema.safeParse({ phoneNumber, otpCode });

    if (!validationResult.success) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_OTP',
          validationResult.error.errors[0]?.message || AUTH_ERROR_MESSAGES.OTP_INVALID,
          'Form validation failed'
        ),
      };
    }

    return verifyOtpCode(validationResult.data.phoneNumber, validationResult.data.otpCode);
  } catch (error) {
    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        AUTH_ERROR_MESSAGES.NETWORK_ERROR,
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Verify OTP code (Direct call version)
 * This version ensures proper parameter handling for direct calls
 */
export async function verifyOtpCodeDirect(
  phoneNumber: string,
  otpCode: string
): Promise<OtpConfirmationResult> {
  'use server';
  return verifyOtpCode(normalizePhoneNumber(phoneNumber), String(otpCode).trim());
}

/**
 * Server Action: Verify OTP code
 * Calls POST /api/v1/authenticator/confirm to validate OTP and complete authentication
 */
export async function verifyOtpCode(
  phoneNumber: string,
  otpCode: string
): Promise<OtpConfirmationResult> {
  try {
    // Normalize parameters
    const normalizedPhoneNumber = normalizePhoneNumber(phoneNumber);
    const normalizedOtpCode = String(otpCode).trim();

    // Validate and split the phone number
    const phoneNumberParts = splitBrazilianPhoneNumber(normalizedPhoneNumber);

    const requestPayload: OtpConfirmationRequest = {
      phoneCode: phoneNumberParts.phoneCode,
      phoneNumber: phoneNumberParts.phoneNumber,
      token: normalizedOtpCode,
    };

    const { data, response } = await authApiRequest<OtpConfirmationResponse>(
      '/authenticator/confirm',
      {
        method: 'POST',
        body: requestPayload,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: mapOtpHttpErrorToAuthError(response.status, errorText),
      };
    }

    // SECURITY: Properly validate the API response
    // The API should return { "message": "Sucess", "status": 200 } for valid OTP
    // We need to validate both the HTTP status AND the response content
    let isSuccess = false;

    if (data.success !== undefined) {
      // If API includes success field, use it
      isSuccess = data.success;
    } else {
      // For APIs without success field, validate the response content
      // Success indicators: message contains "Sucess" or status is 200
      const hasSuccessMessage = data.message && data.message.toLowerCase().includes('sucess');
      const hasSuccessStatus = data.status === 200;

      // Only consider successful if we have clear success indicators
      isSuccess = hasSuccessMessage || hasSuccessStatus;
    }

    if (!isSuccess) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_OTP',
          data.error || data.message || AUTH_ERROR_MESSAGES.OTP_INVALID,
          'API response indicates failure'
        ),
      };
    }

    // Set authentication cookies using the reusable helper
    await setAuthCookies(phoneNumberParts, {
      token: data.token,
      userId: data.userId,
    });

    return {
      success: true,
      data,
    };
  } catch (error) {
    if (error instanceof Error && error.message.includes('Invalid Brazilian')) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          AUTH_ERROR_MESSAGES.PHONE_VALIDATION_ERROR,
          error.message
        ),
      };
    }

    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        AUTH_ERROR_MESSAGES.NETWORK_ERROR,
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Get current authentication status
 * Reads HTTP-only cookies to determine if user is authenticated
 */
export async function getAuthStatus(): Promise<{
  isAuthenticated: boolean;
  phoneNumber: string | null;
  token: string | null;
  userId: string | null;
  isLoading: boolean;
}> {
  const cookieStore = await cookies();

  const token = cookieStore.get(AUTH_COOKIES.TOKEN)?.value || null;
  const userId = cookieStore.get(AUTH_COOKIES.USER_ID)?.value || null;
  const phoneNumber = cookieStore.get(AUTH_COOKIES.PHONE_NUMBER)?.value || null;
  const phoneVerified = cookieStore.get(AUTH_COOKIES.PHONE_VERIFIED)?.value === 'true';

  // User is authenticated if they have either:
  // 1. Full token-based authentication (token + userId)
  // 2. Phone verification-based authentication (phoneVerified + phoneNumber)
  const isAuthenticated = (token && userId) || (phoneVerified && phoneNumber);

  return {
    isAuthenticated: !!isAuthenticated,
    phoneNumber,
    token,
    userId,
    isLoading: false, // Server action always returns loaded state
  };
}

/**
 * Server Action: Logout user
 * Clears authentication cookies
 */
export async function logoutUser(): Promise<void> {
  const cookieStore = await cookies();

  // Clear all authentication cookies using constants
  cookieStore.delete(AUTH_COOKIES.TOKEN);
  cookieStore.delete(AUTH_COOKIES.USER_ID);
  cookieStore.delete(AUTH_COOKIES.PHONE_NUMBER);
  cookieStore.delete(AUTH_COOKIES.PHONE_VERIFIED);
}
